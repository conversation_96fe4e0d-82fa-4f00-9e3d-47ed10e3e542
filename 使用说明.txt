苹果维修配件爬虫 - 使用说明
=====================================

📋 程序功能
-----------
自动爬取苹果维修配件信息，支持多个iPhone型号，生成Markdown格式的配件清单。

🚀 快速开始
-----------
1. 双击 "启动爬虫.bat" 文件
2. 等待程序运行完成
3. 查看 "_output" 文件夹中的结果

📁 文件说明
-----------
启动爬虫.bat        - 完整版启动程序（包含环境检查）
快速启动.bat        - 简化版启动程序（直接运行）
苹果维修配件分类链接表.csv - 输入数据文件
_output/            - 输出文件夹
scraper.log         - 运行日志文件

📊 输出结果
-----------
程序会在 _output 文件夹中生成：
- README.md - 总索引文件
- 8个iPhone型号的维修配件清单文件
- 总计约470个配件信息

⚙️ 系统要求
-----------
- Windows 操作系统
- Python 3.7 或更高版本
- 网络连接

📦 依赖包
---------
程序会自动检查并安装以下依赖包：
- requests (网络请求)
- beautifulsoup4 (HTML解析)
- pandas (数据处理)
- lxml (XML解析)

🔧 故障排除
-----------
1. 如果提示"未检测到Python"：
   - 请先安装Python: https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. 如果程序运行失败：
   - 检查网络连接
   - 查看 scraper.log 文件中的错误信息

3. 如果依赖包安装失败：
   - 手动运行: pip install -r requirements.txt

📞 技术支持
-----------
如遇问题，请查看：
1. scraper.log 日志文件
2. 程序运行时的控制台输出信息

最后更新：2025-06-21
